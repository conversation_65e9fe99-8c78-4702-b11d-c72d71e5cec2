bunx --bun shadcn@2.1.0 init

bunx --bun shadcn@2.1.0 add

export default only for page.tsx and  layout.tsx (this is required for next js)

# How to create dot seperator

I'll explain how these CSS properties work together to create a dotted pattern! It's actually quite clever.

## How the Dotted Pattern Works

The combination creates dots using these key properties:

### 1. **`backgroundImage`** - Creates a Single Dot

```css
backgroundImage: `radial-gradient(circle, ${color} 25%, transparent 25%)`
```

This creates a **radial gradient** that:

- Starts as a solid color (`${color}`) in the center
- At 25% of the radius, it becomes `transparent`
- The result is a **circular dot** surrounded by transparent space

Think of it like this:

- 0% to 25% of radius = solid color (the dot)
- 25% to 100% of radius = transparent (empty space around the dot)

### 2. **`backgroundSize`** - Controls Dot Spacing

```css
// For horizontal direction:
backgroundSize: `${parseInt(dotSize) + parseInt(gapSize)}px ${height}`

// For vertical direction:  
backgroundSize: `${height} ${parseInt(dotSize) + parseInt(gapSize)}px`
```

This sets the **size of each "tile"** that contains one dot. The tile size is:

- **Dot size** + **Gap size** = Total space per dot
- Example: `2px dot + 6px gap = 8px tile`

### 3. **`backgroundRepeat`** - Repeats the Pattern

```css
backgroundRepeat: isHorizontal ? "repeat-x" : "repeat-y"
```

This tiles the single dot across the entire element:

- `repeat-x` = repeat horizontally (for horizontal lines)
- `repeat-y` = repeat vertically (for vertical lines)

## Create dot with shadcn Separator

<Separator className="bg-transparent border-dotted border-t-2 border-red-500" />

# Hono

Hono can help solve Next.js API Route Limitations, which are:
- Type Safety Issues
    - No end-to-end type safety between frontend and backend
- File-Based Routing Problems
    - Folder-based routing becomes complex and hard to manage
    - Parameter inference issues (misspelling route parameters), example if folder name is /api/users/[userId] and when you try to params userid, it will not throw an error


# Usage

- bun run dev